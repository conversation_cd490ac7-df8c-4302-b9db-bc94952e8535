<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/fy.php', [
    'formats' => [
        'L' => 'DD-MM-YY',
    ],
    'months' => ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Ma<PERSON>', 'April', '<PERSON><PERSON>e', 'Juny', 'July', 'Augustus', 'Septimber', 'Okto<PERSON>', 'Novimber', 'Desimber'],
    'months_short' => ['Jan', 'Feb', 'Mrt', 'Apr', 'Mai', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'],
    'weekdays' => ['Snein', '<PERSON><PERSON><PERSON>', 'Tiis<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Freed', '<PERSON>ne<PERSON>'],
    'weekdays_short' => ['Sn', 'Mo', 'Ti', 'Wo', 'To', 'Fr', 'Sn'],
    'weekdays_min' => ['Sn', 'Mo', 'Ti', 'Wo', 'To', 'Fr', 'Sn'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 4,
]);
