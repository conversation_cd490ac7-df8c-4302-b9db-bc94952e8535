<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays' => ['Lumingu', 'Nkodya', 'Nd<PERSON><PERSON><PERSON>', 'Ndangù', 'Nj<PERSON>wa', 'Ngòvya', 'Lubingu'],
    'weekdays_short' => ['Lum', 'Nko', 'Ndy', 'Ndg', 'Njw', 'Ngv', 'Lub'],
    'weekdays_min' => ['Lum', 'Nko', 'Ndy', 'Ndg', 'Njw', 'Ngv', 'Lub'],
    'months' => ['Ciongo', 'Lùishi', 'Lusòlo', '<PERSON><PERSON><PERSON><PERSON>', 'Lumùngùl<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ì<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ng<PERSON><PERSON>', '<PERSON>sw<PERSON><PERSON><PERSON><PERSON>', '<PERSON>isw<PERSON>'],
    'months_short' => ['<PERSON><PERSON>', 'Lu<PERSON>', 'Lus', 'Muu', 'Lum', 'Luf', 'Kab', 'Lush', 'Lut', 'Lun', 'Kas', 'Cis'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd D MMMM YYYY HH:mm',
    ],
]);
