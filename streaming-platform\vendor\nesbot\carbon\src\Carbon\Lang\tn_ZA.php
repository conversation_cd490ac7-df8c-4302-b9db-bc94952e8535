<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD/MM/YYYY',
    ],
    'months' => ['<PERSON>rikgong', 'Tlhakole', 'Mopitlwe', 'Moran<PERSON>', 'Motsheganong', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ukwi', '<PERSON>atwe', '<PERSON>wetse', '<PERSON><PERSON><PERSON>', 'Ngwana<PERSON><PERSON>', 'Sedimonthole'],
    'months_short' => ['Fer', 'Tlh', 'Mop', 'Mor', 'Mot', 'See', 'Phu', 'Pha', 'Lwe', 'Dip', 'Ngw', 'Sed'],
    'weekdays' => ['la<PERSON><PERSON>i', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['Tsh', 'Mo<PERSON>', 'Bed', '<PERSON>r', 'Ne', 'Tlh', 'Mat'],
    'weekdays_min' => ['Tsh', 'Mos', 'Bed', 'Rar', 'Ne', 'Tlh', 'Mat'],
    'first_day_of_week' => 0,
    'day_of_first_week_of_year' => 1,

    'year' => 'dingwaga di le :count',
    'y' => 'dingwaga di le :count',
    'a_year' => 'dingwaga di le :count',

    'month' => 'dikgwedi di le :count',
    'm' => 'dikgwedi di le :count',
    'a_month' => 'dikgwedi di le :count',

    'week' => 'dibeke di le :count',
    'w' => 'dibeke di le :count',
    'a_week' => 'dibeke di le :count',

    'day' => 'malatsi :count',
    'd' => 'malatsi :count',
    'a_day' => 'malatsi :count',

    'hour' => 'diura di le :count',
    'h' => 'diura di le :count',
    'a_hour' => 'diura di le :count',

    'minute' => 'metsotso e le :count',
    'min' => 'metsotso e le :count',
    'a_minute' => 'metsotso e le :count',

    'second' => 'metsotswana e le :count',
    's' => 'metsotswana e le :count',
    'a_second' => 'metsotswana e le :count',
]);
