# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](https://keepachangelog.com/) principles.

## [5.0.2] - 2024-10-09

### Changed

* [#21](https://github.com/sebastianbergmann/version/pull/21): Avoid spawning a shell for Git

## [5.0.1] - 2024-07-03

### Changed

* This project now uses PHPStan instead of Psalm for static analysis

## [5.0.0] - 2024-02-02

### Removed

* This component is no longer supported on PHP 8.1

## [4.0.1] - 2023-02-07

### Fixed

* [#17](https://github.com/sebastianbergmann/version/pull/17): Release archive contains unnecessary assets

## [4.0.0] - 2023-02-03

### Changed

* `Version::getVersion()` has been renamed to `Version::asString()`

### Removed

* This component is no longer supported on PHP 7.3, PHP 7.4, and PHP 8.0

## [3.0.2] - 2020-09-28

### Changed

* Changed PHP version constraint in `composer.json` from `^7.3 || ^8.0` to `>=7.3`

## [3.0.1] - 2020-06-26

### Added

* This component is now supported on PHP 8

## [3.0.0] - 2020-01-21

### Removed

* This component is no longer supported on PHP 7.1 and PHP 7.2

[5.0.2]: https://github.com/sebastianbergmann/version/compare/5.0.1...5.0.2
[5.0.1]: https://github.com/sebastianbergmann/version/compare/5.0.0...5.0.1
[5.0.0]: https://github.com/sebastianbergmann/version/compare/4.0...5.0.0
[4.0.1]: https://github.com/sebastianbergmann/version/compare/4.0.0...4.0.1
[4.0.0]: https://github.com/sebastianbergmann/version/compare/3.0.2...4.0.0
[3.0.2]: https://github.com/sebastianbergmann/version/compare/3.0.1...3.0.2
[3.0.1]: https://github.com/sebastianbergmann/version/compare/3.0.0...3.0.1
[3.0.0]: https://github.com/sebastianbergmann/version/compare/2.0.1...3.0.0
